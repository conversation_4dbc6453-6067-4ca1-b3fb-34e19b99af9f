import pandas as pd

def load_logs(file_path: str) -> pd.DataFrame:
    """
    Lädt die geparsten Log-Daten aus einer CSV-Datei.
    Konvertiert die 'timestamp'-Spalte in datetime-Objekte.
    """
    try:
        df = pd.read_csv(file_path)
        # Si<PERSON><PERSON><PERSON>, dass 'timestamp' als datetime-Objekt vorliegt
        df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
        # Zeilen mit ungültigen Timestamps entfernen
        df.dropna(subset=['timestamp'], inplace=True)
        return df
    except FileNotFoundError:
        print(f"Fehler: Die Datei '{file_path}' wurde nicht gefunden.")
        return pd.DataFrame()
    except pd.errors.EmptyDataError:
        print(f"Fehler: Die Datei '{file_path}' ist leer.")
        return pd.DataFrame()
    except Exception as e:
        print(f"Ein Fehler ist beim Laden der Datei aufgetreten: {e}")
        return pd.DataFrame()

def filter_by_pid(df: pd.DataFrame):
    """
    Filtert Logs nach einer bestimmten Prozess-ID (PID).
    """
    if df.empty:
        print("Keine Daten zum Filtern vorhanden.")
        return

    while True:
        pid_input = input("Bitte geben Sie die PID ein, nach der gefiltert werden soll (oder 'q' zum Beenden): ").strip()
        if pid_input.lower() == 'q':
            return
        
        try:
            pid = int(pid_input)
            filtered_df = df[df['pid'] == pid]
            if not filtered_df.empty:
                print(f"\n--- Logs für PID: {pid} ---")
                print(filtered_df.to_string())
            else:
                print(f"Keine Logs für PID: {pid} gefunden.")
            break
        except ValueError:
            print("Ungültige PID. Bitte geben Sie eine ganze Zahl ein.")

def filter_by_call_sid_session(df: pd.DataFrame):
    """
    Zeigt eine Liste von 'call_sid's für 'call_started'-Ereignisse an
    und filtert dann alle zugehörigen Ereignisse bis 'session_closed'.
    """
    if df.empty:
        print("Keine Daten zum Filtern vorhanden.")
        return

    # Finde alle eindeutigen call_sids, die mit 'call_started' beginnen
    call_started_sids = df[df['event'] == 'call_started']['call_sid'].unique()

    if len(call_started_sids) == 0:
        print("Keine 'call_started'-Ereignisse in den Logs gefunden.")
        return

    print("\n--- Verfügbare Call SIDs (call_started) ---")
    for i, sid in enumerate(call_started_sids):
        # Versuche, den Zeitstempel des call_started-Ereignisses zu finden
        start_time = df[(df['call_sid'] == sid) & (df['event'] == 'call_started')]['timestamp'].min()
        print(f"{i + 1}. {sid} (Gestartet: {start_time if pd.notna(start_time) else 'N/A'})")

    while True:
        selection = input("Bitte wählen Sie die Nummer einer Call SID aus (oder 'q' zum Beenden): ").strip()
        if selection.lower() == 'q':
            return

        try:
            index = int(selection) - 1
            if 0 <= index < len(call_started_sids):
                selected_sid = call_started_sids[index]
                
                # Filtere alle Ereignisse für die ausgewählte call_sid
                session_logs = df[df['call_sid'] == selected_sid].sort_values(by='timestamp')
                
                if not session_logs.empty:
                    print(f"\n--- Alle Ereignisse für Call SID: {selected_sid} ---")
                    print(session_logs.to_string())
                else:
                    print(f"Keine Ereignisse für Call SID: {selected_sid} gefunden.")
                break
            else:
                print("Ungültige Auswahl. Bitte geben Sie eine gültige Nummer ein.")
        except ValueError:
            print("Ungültige Eingabe. Bitte geben Sie eine Nummer oder 'q' ein.")

def main():
    log_file = 'g:/Ai-Assistent-Technology/Kunden/Medizentrum Eckert/Logs/parsed_logs.csv'
    logs_df = load_logs(log_file)

    if logs_df.empty:
        print("Das Skript kann ohne Log-Daten nicht fortgesetzt werden.")
        return

    while True:
        print("\n--- Log-Filter-Menü ---")
        print("1. Logs nach PID filtern")
        print("2. Logs nach Call SID (Sitzungsverlauf) filtern")
        print("3. Beenden")

        choice = input("Bitte wählen Sie eine Option: ").strip()

        if choice == '1':
            filter_by_pid(logs_df)
        elif choice == '2':
            filter_by_call_sid_session(logs_df)
        elif choice == '3':
            print("Skript wird beendet.")
            break
        else:
            print("Ungültige Option. Bitte wählen Sie 1, 2 oder 3.")

if __name__ == "__main__":
    main()