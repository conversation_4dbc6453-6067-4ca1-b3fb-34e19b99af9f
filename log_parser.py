import pandas as pd
import json

# CSV-Datei einlesen
df = pd.read_csv('Explore-data-2025-06-22 17_07_56.csv')

# <PERSON><PERSON> zum Parsen der JSON-Logzeilen
def parse_log_line(line):
    try:
        # JSON-String parsen
        log_data = json.loads(line)
        
        # Ba<PERSON>felder extrahieren
        result = {
            'level': log_data.get('level'),
            'timestamp': log_data.get('timestamp'),
            'pid': log_data.get('pid'),
            'hostname': log_data.get('hostname'),
            'call_sid': log_data.get('call_sid'),
            'component': log_data.get('component'),
            'event': log_data.get('event'),
            'service': log_data.get('service'),
            'environment': log_data.get('environment'),
            'msg': log_data.get('msg')
        }
        
        # Zusätzliche Felder je nach Ereignistyp
        if 'event_data' in log_data and isinstance(log_data['event_data'], dict):
            for key, value in log_data['event_data'].items():
                result[f'event_data_{key}'] = value
                
        # Weitere spezifische Felder
        for key in ['close_code', 'close_reason', 'session_duration', 
                   'completion_reason', 'call_status', 'duration', 
                   'error', 'sip_details', 'event_type']:
            if key in log_data:
                result[key] = log_data[key]
        
        return result
    except json.JSONDecodeError:
        return {'parse_error': line}

# Logzeilen parsen und in neue DataFrame umwandeln
parsed_logs = []
for line in df['Line']:
    parsed_logs.append(parse_log_line(line))

# Neue DataFrame erstellen
parsed_df = pd.DataFrame(parsed_logs)

# Ursprüngliche Metadaten beibehalten
parsed_df['original_time'] = df['Time']
parsed_df['tsNs'] = df['tsNs']
parsed_df['id'] = df['id']

# Ergebnis speichern
parsed_df.to_csv('parsed_logs.csv', index=False)